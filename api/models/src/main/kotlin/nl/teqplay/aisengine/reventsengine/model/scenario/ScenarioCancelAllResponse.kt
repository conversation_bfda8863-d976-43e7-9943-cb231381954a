package nl.teqplay.aisengine.reventsengine.model.scenario

/**
 * Response model for the cancel all scenarios operation.
 */
data class ScenarioCancelAllResponse(
    /**
     * Number of scenarios that were cancelled by this operation.
     */
    val totalCancelled: Int,
    
    /**
     * Number of scenarios that were already in CANCELLED state.
     */
    val alreadyCancelled: Int,
    
    /**
     * IDs of scenarios that were cancelled by this operation.
     */
    val cancelledScenarioIds: List<String>,
    
    /**
     * IDs of scenarios that were already in CANCELLED state.
     */
    val alreadyCancelledScenarioIds: List<String>
)
