package nl.teqplay.aisengine.areamonitor.service

import io.github.oshai.kotlinlogging.KotlinLogging
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.skeleton.metrics.Metric
import nl.teqplay.skeleton.metrics.MetricRegistry
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.util.concurrent.atomic.AtomicLong

/**
 * Service to monitor event publishing success/failure rates and detect potential issues
 * with missing AreaEndEvents or other critical events.
 */
@Component
class EventPublishingMonitorService(
    meterRegistry: MeterRegistry
) {
    private val log = KotlinLogging.logger { }
    private val registry = MetricRegistry.of<EventPublishingMonitorService>(meterRegistry)

    // Metrics for tracking event publishing
    private val areaStartEventsPublished = registry.createGauge("area_start_events_published", AtomicLong())
    private val areaEndEventsPublished = registry.createGauge("area_end_events_published", AtomicLong())
    private val areaStartEventsFailed = registry.createGauge("area_start_events_failed", AtomicLong())
    private val areaEndEventsFailed = registry.createGauge("area_end_events_failed", AtomicLong())
    private val totalEventsPublished = registry.createGauge("total_events_published", AtomicLong())
    private val totalEventsFailed = registry.createGauge("total_events_failed", AtomicLong())

    // Counters for rate calculations
    private val recentStartEventsPublished = AtomicLong()
    private val recentEndEventsPublished = AtomicLong()
    private val recentStartEventsFailed = AtomicLong()
    private val recentEndEventsFailed = AtomicLong()

    /**
     * Record a successful event publication
     */
    fun recordEventPublished(event: AreaEvent) {
        totalEventsPublished.incrementAndGet()
        
        when (event) {
            is AreaStartEvent -> {
                areaStartEventsPublished.incrementAndGet()
                recentStartEventsPublished.incrementAndGet()
            }
            is AreaEndEvent -> {
                areaEndEventsPublished.incrementAndGet()
                recentEndEventsPublished.incrementAndGet()
            }
        }
    }

    /**
     * Record a failed event publication
     */
    fun recordEventFailed(event: AreaEvent, error: Throwable) {
        totalEventsFailed.incrementAndGet()
        
        when (event) {
            is AreaStartEvent -> {
                areaStartEventsFailed.incrementAndGet()
                recentStartEventsFailed.incrementAndGet()
                log.warn { "AreaStartEvent failed for MMSI ${event.ship.mmsi} in area ${event.area.id}: ${error.message}" }
            }
            is AreaEndEvent -> {
                areaEndEventsFailed.incrementAndGet()
                recentEndEventsFailed.incrementAndGet()
                log.error { "CRITICAL: AreaEndEvent failed for MMSI ${event.ship.mmsi} in area ${event.area.id}: ${error.message}" }
            }
        }
    }

    /**
     * Periodic health check and alerting
     */
    @Scheduled(fixedRate = 60_000) // Every minute
    fun healthCheck() {
        val startPublished = recentStartEventsPublished.getAndSet(0)
        val endPublished = recentEndEventsPublished.getAndSet(0)
        val startFailed = recentStartEventsFailed.getAndSet(0)
        val endFailed = recentEndEventsFailed.getAndSet(0)

        val totalPublished = startPublished + endPublished
        val totalFailed = startFailed + endFailed

        if (totalPublished > 0 || totalFailed > 0) {
            val failureRate = if (totalPublished + totalFailed > 0) {
                (totalFailed.toDouble() / (totalPublished + totalFailed)) * 100
            } else 0.0

            log.info { 
                "Event publishing stats (last minute): " +
                "Start events: ${startPublished} published, ${startFailed} failed | " +
                "End events: ${endPublished} published, ${endFailed} failed | " +
                "Failure rate: ${"%.2f".format(failureRate)}%"
            }

            // Alert on high failure rates
            if (failureRate > 5.0) {
                log.warn { "HIGH FAILURE RATE DETECTED: ${"%.2f".format(failureRate)}% of events failed to publish in the last minute!" }
            }

            // Critical alert for any AreaEndEvent failures
            if (endFailed > 0) {
                log.error { "CRITICAL ALERT: ${endFailed} AreaEndEvent(s) failed to publish in the last minute! This may result in vessels appearing stuck in areas." }
            }
        }
    }

    /**
     * Detect potential imbalances between start and end events
     */
    @Scheduled(fixedRate = 300_000) // Every 5 minutes
    fun detectEventImbalances() {
        val totalStartEvents = areaStartEventsPublished.get()
        val totalEndEvents = areaEndEventsPublished.get()
        val startFailures = areaStartEventsFailed.get()
        val endFailures = areaEndEventsFailed.get()

        // Calculate expected vs actual ratios
        val expectedEndEvents = totalStartEvents - startFailures
        val actualEndEvents = totalEndEvents
        val missingEndEvents = expectedEndEvents - actualEndEvents - endFailures

        if (missingEndEvents > 10) { // Threshold for alerting
            log.warn { 
                "POTENTIAL EVENT IMBALANCE DETECTED: " +
                "Expected ~${expectedEndEvents} end events based on ${totalStartEvents} start events, " +
                "but only ${actualEndEvents} end events published. " +
                "Approximately ${missingEndEvents} end events may be missing. " +
                "This could indicate vessels stuck in areas due to missing AreaEndEvents."
            }
        }

        log.debug { 
            "Event balance check: " +
            "Start events: ${totalStartEvents} (${startFailures} failed), " +
            "End events: ${actualEndEvents} (${endFailures} failed), " +
            "Estimated missing: ${missingEndEvents}"
        }
    }

    /**
     * Get current metrics for external monitoring
     */
    fun getMetrics(): EventPublishingMetrics {
        return EventPublishingMetrics(
            areaStartEventsPublished = areaStartEventsPublished.get(),
            areaEndEventsPublished = areaEndEventsPublished.get(),
            areaStartEventsFailed = areaStartEventsFailed.get(),
            areaEndEventsFailed = areaEndEventsFailed.get(),
            totalEventsPublished = totalEventsPublished.get(),
            totalEventsFailed = totalEventsFailed.get()
        )
    }

    data class EventPublishingMetrics(
        val areaStartEventsPublished: Long,
        val areaEndEventsPublished: Long,
        val areaStartEventsFailed: Long,
        val areaEndEventsFailed: Long,
        val totalEventsPublished: Long,
        val totalEventsFailed: Long
    ) {
        val failureRate: Double
            get() = if (totalEventsPublished + totalEventsFailed > 0) {
                (totalEventsFailed.toDouble() / (totalEventsPublished + totalEventsFailed)) * 100
            } else 0.0

        val estimatedMissingEndEvents: Long
            get() = (areaStartEventsPublished - areaStartEventsFailed) - areaEndEventsPublished - areaEndEventsFailed
    }
}
