nats:
  ais-stream:
    enabled: true
    url: nats://localhost:4222
    username: ais-stream-consume-stop-monitor
    password:

  event-stream:
    enabled: true
    url: nats://localhost:4222
    username: event-publish-stop-monitor
    password:

# Prometheus JVM stats exposing
management:
  metrics:
    enable:
      jvm: true
    tags:
      component: stop-monitor
    logging:
      enabled: true
      metric-names: message.count.eventhandlerservice

  endpoints:
    web:
      exposure:
        include: health,prometheus

spring:
  data:
    redis:
      host: localhost
      port: 6379
      password: