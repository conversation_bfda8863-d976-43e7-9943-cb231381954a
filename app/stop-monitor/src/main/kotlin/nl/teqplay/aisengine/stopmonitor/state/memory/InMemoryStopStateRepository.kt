package nl.teqplay.aisengine.stopmonitor.state.memory

import nl.teqplay.aisengine.revents.ReventsProfile
import nl.teqplay.aisengine.stopmonitor.model.StopState
import nl.teqplay.aisengine.stopmonitor.state.interfaces.StopStateRepository
import org.springframework.stereotype.Repository
import java.util.concurrent.ConcurrentHashMap

/**
 * In-memory implementation of the StateRepository interface.
 * This implementation stores data in a ConcurrentHashMap for thread safety.
 */
@ReventsProfile
@Repository
class InMemoryStopStateRepository : StopStateRepository {
    private val store = ConcurrentHashMap<Int, StopState>()

    override fun findByMmsi(mmsi: Int): StopState? {
        return store[mmsi]
    }

    override fun save(entity: StopState): StopState {
        store[entity.mmsi] = entity
        return entity
    }

    override fun deleteById(id: Int) {
        store.remove(id)
    }

    override fun count(): Long {
        return store.size.toLong()
    }
}
