package nl.teqplay.aisengine.stopmonitor.state.redis

import nl.teqplay.aisengine.revents.NotReventsProfile
import nl.teqplay.aisengine.stopmonitor.model.StopTraceState
import nl.teqplay.aisengine.stopmonitor.state.interfaces.StopTraceStateCrudRepository
import nl.teqplay.aisengine.stopmonitor.state.interfaces.StopTraceStateRepository
import org.springframework.stereotype.Repository
import kotlin.jvm.optionals.getOrNull

@NotReventsProfile
@Repository
class RedisStopTraceStateRepository(private val repository: StopTraceStateCrudRepository) :
    StopTraceStateRepository {
    override fun findByMmsi(mmsi: Int): StopTraceState? {
        return repository.findById(mmsi).getOrNull()
    }

    override fun save(entity: StopTraceState): StopTraceState {
        return repository.save(entity)
    }

    override fun deleteById(mmsi: Int) {
        repository.deleteById(mmsi)
    }

    override fun count(): Long {
        return repository.count()
    }
}
