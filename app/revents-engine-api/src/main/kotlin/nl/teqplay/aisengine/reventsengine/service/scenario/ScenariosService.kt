package nl.teqplay.aisengine.reventsengine.service.scenario

import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.reventsengine.common.datasource.ScenariosDataSource
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioMetadata
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.CANCELLED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.Companion.FINAL_PHASES
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.FORKED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.PRUNED
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosMetadataService
import nl.teqplay.aisengine.reventsengine.model.interest.InterestRelevantShip
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioCancelAllResponse
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioCreateRequest
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioPostProcessing
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioQueryResponse
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioResponse
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioResponse.Phase.CRASHED
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioResponse.Phase.FINISHED
import nl.teqplay.aisengine.reventsengine.service.scenario.resolver.ScenariosResolverService
import nl.teqplay.aisengine.util.coerceTimeWindowWithinBoundary
import nl.teqplay.aisengine.util.contains
import nl.teqplay.skeleton.auth.credentials.User
import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.skeleton.common.exception.NotFoundException
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.Instant

@Component
class ScenariosService(
    private val scenariosDataSource: ScenariosDataSource,
    private val scenariosSchedulerService: ScenariosSchedulerService,
    private val scenariosResolverService: ScenariosResolverService,
    private val scenariosInterestsService: ScenariosInterestsService,
    private val scenariosMetadataService: ScenariosMetadataService,
    private val scenariosPruneService: ScenariosPruneService,
) {

    /**
     * Creates a [scenario] and starts the scheduler.
     */
    fun create(scenario: ScenarioCreateRequest): ScenarioResponse {
        val resolvedScenario = resolveScenarioCreateRequest(scenario)
        val state = ScenarioState(resolvedScenario)
        return saveNewState(state)
    }

    /**
     * Resolves [scenario] by preparing the required [Scenario.events] and [Scenario.postProcessing].
     * For the specified [Scenario.postProcessing] and [Scenario.guarantees].
     */
    private fun resolveScenarioCreateRequest(
        scenario: ScenarioCreateRequest,
    ): ScenarioCreateRequest {
        SecurityContextHolder.getContext()
        var windowMargin = scenario.windowMargin
        val postProcessing = scenario.postProcessing.toMutableSet()
        var settings = scenario.settings
        var metadata = scenario.metaData?.copy(
            createdBy = scenario.metaData?.createdBy ?: "",
            requestFromSystem = getCurrentUser().username,
        )

        // Based on guarantees, add required steps.
        scenario.guarantees.forEach { guarantee ->
            guarantee.validateInterests(scenario.interests)

            windowMargin = guarantee.adjustWindowMargin(windowMargin)
            postProcessing += guarantee.postProcessing
            settings = guarantee.adjustSettings(settings ?: Scenario.Settings(scenario))
        }

        val resolvedEvents = when {
            (settings ?: Scenario.Settings(scenario)).resolveScenarioEventsByPostProcessing ->
                scenariosResolverService.resolveEvents(scenario.events, postProcessing)

            else -> scenariosResolverService.resolveEvents(scenario.events, emptySet())
        }
        return scenario.copy(
            windowMargin = windowMargin,
            events = resolvedEvents,
            postProcessing = postProcessing,
            settings = settings,
            metaData = metadata
        )
    }

    private fun saveNewState(state: ScenarioState): ScenarioResponse {
        scenariosDataSource.save(state)
        scenariosSchedulerService.loopOverScenarios()

        // state can't be forked yet, so no child scenarios will exist
        val childScenarios = emptyList<ScenarioState>()
        return state.toResponse(childScenarios, emptyList())
    }

    fun createForAfterwardsPostProcessing(
        id: String,
        postProcessing: Set<ScenarioPostProcessing>,
    ): ScenarioResponse {
        if (postProcessing.isEmpty()) throw BadRequestException("Post-processing should not be empty")

        val state = scenariosDataSource.get(id) ?: throw NotFoundException("Scenario not found")

        val requiredEvents = postProcessing
            .flatMap { scenariosResolverService.resolveEvents(it.events, emptySet()) }
            .toSet()

        if (requiredEvents.any { it !in state.events }) {
            throw BadRequestException("Scenario does not meet pre-requisites for post-processing step")
        }

        val response = state.toResponse()
        if (response.phase != FINISHED) {
            throw BadRequestException("Scenario not in FINISHED phase")
        }

        // Create a new scenario, which inherits from the input scenario but setting the post-processing steps.
        val newCreateRequest = ScenarioCreateRequest(state)
        val newState = ScenarioState(newCreateRequest).copy(
            postProcessing = postProcessing,
            inherit = id
        )
        return saveNewState(newState)
    }

    /**
     * Get a scenario by its [id], turning it into a [ScenarioResponse].
     */
    fun get(id: String): ScenarioResponse? {
        val state = scenariosDataSource.get(id)
        return state?.toResponse()
    }

    /**
     * Prune a scenario by its [id].
     */
    fun prune(id: String): ScenarioResponse? {
        val state = scenariosDataSource.get(id) ?: return null
        return when (state.phase) {
            PRUNED -> state.toResponse()
            in FINAL_PHASES -> scenariosPruneService.prune(state).toResponse()
            else -> throw BadRequestException("Scenario is not in a final phase to be pruned")
        }
    }

    fun cancel(id: String): ScenarioResponse? {
        val state = scenariosDataSource.get(id) ?: return null
        return when (state.phase) {
            CANCELLED -> state.toResponse()
            !in FINAL_PHASES -> {
                val cancelledState = state.copy(
                    phase = CANCELLED,
                    cancelled = Instant.now()
                )
                scenariosDataSource.save(cancelledState)
                cancelledState.toResponse()
            }
            else -> throw BadRequestException("Scenario is in a final phase and can't be cancelled")
        }
    }

    /**
     * Cancel all scenarios that are not in final phases.
     * Returns a summary of the cancellation operation.
     */
    fun cancelAll(): ScenarioCancelAllResponse {
        val availableScenarios = scenariosDataSource.getAvailableScenarios()
        val cancelledScenarios = mutableListOf<ScenarioState>()
        val alreadyCancelledScenarios = mutableListOf<ScenarioState>()

        val now = Instant.now()

        availableScenarios.forEach { scenario ->
            when (scenario.phase) {
                CANCELLED -> alreadyCancelledScenarios.add(scenario)
                !in FINAL_PHASES -> {
                    val cancelledState = scenario.copy(
                        phase = CANCELLED,
                        cancelled = now
                    )
                    cancelledScenarios.add(cancelledState)
                }
                // This shouldn't happen since getAvailableScenarios() filters out final phases,
                // but we handle it for safety
                else -> { /* Skip scenarios in final phases */ }
            }
        }

        // Save all cancelled scenarios in bulk
        if (cancelledScenarios.isNotEmpty()) {
            scenariosDataSource.saveMany(cancelledScenarios)
        }

        return ScenarioCancelAllResponse(
            totalCancelled = cancelledScenarios.size,
            alreadyCancelled = alreadyCancelledScenarios.size,
            cancelledScenarioIds = cancelledScenarios.map { it.id },
            alreadyCancelledScenarioIds = alreadyCancelledScenarios.map { it.id }
        )
    }

    private fun ScenarioState.toResponse(): ScenarioResponse {
        val childScenarios = when (phase) {
            FORKED -> scenariosDataSource.getChildScenarios(id)
            else -> emptyList()
        }
        val inheritedScenarios = scenariosDataSource.getInheritedScenarios(id)
        return toResponse(childScenarios, inheritedScenarios)
    }

    /**
     * Query scenarios for overlap with the queried [scenario].
     */
    fun query(scenario: ScenarioCreateRequest): ScenarioQueryResponse {
        val scenarios = scenariosDataSource
            .query(scenario.window, scenario.interests)
            .mapToResponses()
            // additional filter on crashed, we could have a forked scenario
            // which has at least one crashed child scenario
            .filter { it.phase != CRASHED }

        val matches = mutableListOf<ScenarioResponse>()
        val overlaps = mutableListOf<ScenarioResponse>()
        scenarios.forEach { queriedScenario ->
            if (queriedScenario.window.contains(scenario.window)) {
                matches.add(queriedScenario)
            } else {
                overlaps.add(queriedScenario)
            }
        }

        return ScenarioQueryResponse(
            matches = matches.sortedByDescending { it.queued },
            overlaps = overlaps.sortedWith(
                compareByDescending<ScenarioResponse> {
                    // value scenarios with more overlap more highly
                    val window = coerceTimeWindowWithinBoundary(it.window, scenario.window)
                    Duration.between(window.from, window.to)
                }.thenByDescending { it.queued }
            )
        )
    }

    /**
     * Get scenarios by their [ids], turning them into a [ScenarioResponse].
     */
    fun getByIds(ids: Set<String>): List<ScenarioResponse> {
        val state = scenariosDataSource.getByIds(ids)
        return state.mapToResponses()
    }

    /**
     * Get the interests of a scenario by its [id].
     */
    fun getInterests(id: String): List<InterestRelevantShip>? {
        val state = scenariosDataSource.get(id) ?: return null

        val interests = scenariosInterestsService.getInterestsByScenario(state.getSourceScenarioId())
        return interests.map {
            InterestRelevantShip(
                ship = AisShipIdentifier(
                    mmsi = it.mmsi,
                    imo = it.imo
                ),
                window = it.window,
                windowNoMargin = it.windowNoMargin,
                partial = it.partial
            )
        }
    }

    /**
     * Get the metadata of a scenario by its [id].
     */
    fun getMetadata(id: String): ScenarioMetadata? {
        val state = scenariosDataSource.get(id) ?: return null
        return scenariosMetadataService.getMetadataByScenario(state.getSourceScenarioId())
    }

    private fun List<ScenarioState>.mapToResponses(): List<ScenarioResponse> {
        val parentScenarioIds = filter { it.phase == FORKED }.map { it.id }.toSet()
        val childScenariosMap = scenariosDataSource
            .getChildScenarios(parentScenarioIds)
            .groupBy { it.parent }

        // Only contain scenarios that are inheritable.
        val inheritableScenarioIds = filter { it.parent == null && it.inherit == null }.map { it.id }.toSet()
        val inheritedScenariosMap = scenariosDataSource
            .getInheritedScenarios(inheritableScenarioIds)
            .groupBy { it.inherit }

        return map {
            val childScenarios = childScenariosMap[it.id] ?: emptyList()
            val inheritedScenarios = inheritedScenariosMap[it.id] ?: emptyList()
            it.toResponse(childScenarios, inheritedScenarios)
        }
    }

    fun getCurrentUser() = SecurityContextHolder.getContext().authentication.principal as User
}
